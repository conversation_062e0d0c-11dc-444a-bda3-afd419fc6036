# Makefile for Svelte 5 + SvelteKit + shadcn-svelte Docker operations
DC=docker compose
.PHONY: help dev prod dev-build prod-build dev-up prod-up dev-down prod-down dev-logs prod-logs clean

# Default target
help:
	@echo "Available commands:"
	@echo "  dev          - Start development environment"
	@echo "  prod         - Start production environment"
	@echo "  dev-build    - Build development image"
	@echo "  prod-build   - Build production image"
	@echo "  dev-up       - Start development services"
	@echo "  prod-up      - Start production services"
	@echo "  dev-down     - Stop development services"
	@echo "  prod-down    - Stop production services"
	@echo "  dev-logs     - View development logs"
	@echo "  prod-logs    - View production logs"
	@echo "  clean        - Remove all containers and images"

# Development commands
dev: dev-build dev-up

dev-build:
	${DC} -f docker-compose.dev.yml build

dev-up:
	${DC} -f docker-compose.dev.yml up

dev-down:
	${DC} -f docker-compose.dev.yml down

dev-logs:
	${DC} -f docker-compose.dev.yml logs -f

# Production commands
prod: prod-build prod-up

prod-build:
	${DC} -f docker-compose.prod.yml build

prod-up:
	${DC} -f docker-compose.prod.yml up -d

prod-down:
	${DC} -f docker-compose.prod.yml down

prod-logs:
	${DC} -f docker-compose.prod.yml logs -f

# Cleanup
clean:
	${DC} -f docker-compose.dev.yml down --rmi all --volumes --remove-orphans
	${DC} -f docker-compose.prod.yml down --rmi all --volumes --remove-orphans
	docker system prune -f
