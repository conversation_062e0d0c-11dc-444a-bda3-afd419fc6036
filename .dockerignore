# Node modules (will be installed in container)
node_modules
npm-debug.log*

# Build outputs
.output
.vercel
.netlify
.wrangler
/.svelte-kit
/build

# Test results
test-results
coverage

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Environment files
.env
.env.*
!.env.example
!.env.test

# IDE files
.vscode
.idea
*.swp
*.swo
*~

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Vite
vite.config.js.timestamp-*
vite.config.ts.timestamp-*

# Docker files (avoid recursive copying)
Dockerfile*
.dockerignore

# Git
.git
.gitignore

# Documentation
README.md
*.md
