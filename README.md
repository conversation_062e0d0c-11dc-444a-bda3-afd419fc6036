# svelte5-shadcn-app

## Svelte 5 + SvelteKit + shadcn-svelte

A modern, maintainable frontend component library built with the latest technologies and best practices.

## 🚀 Features

- **Svelte 5** with runes for reactive state management
- **SvelteKit** for full-stack web application framework
- **shadcn-svelte** for beautiful, accessible UI components
- **TailwindCSS 4.0** for utility-first styling
- **TypeScript** for type safety
- **Vitest** for unit testing
- **Playwright** for end-to-end testing
- **ESLint + Prettier** for code quality
- **Pre-commit hooks** for automated quality checks
- **VS Code** configuration for optimal development experience

## 📦 Tech Stack

- [Svelte 5](https://svelte.dev/) - Cybernetically enhanced web apps
- [SvelteKit](https://kit.svelte.dev/) - The fastest way to build svelte apps
- [shadcn-svelte](https://shadcn-svelte.com/) - Beautifully designed components
- [TailwindCSS](https://tailwindcss.com/) - A utility-first CSS framework
- [TypeScript](https://www.typescriptlang.org/) - JavaScript with syntax for types
- [Vite](https://vitejs.dev/) - Next generation frontend tooling

## 🛠️ Getting Started

### Prerequisites

- Node.js 18+
- npm, pnpm, yarn, or bun

### Installation

1. Clone this repository or create a new project:

```bash
npx sv create my-app
cd my-app
```

2. Install dependencies:

```bash
npm install
```

3. Initialize shadcn-svelte:

```bash
npx shadcn-svelte@latest init
```

4. Start the development server:

```bash
npm run dev
```

5. Open [http://localhost:5173](http://localhost:5173) in your browser.

### 🔧 Pre-commit Setup (Recommended)

This project includes pre-commit hooks for automated code quality checks. To set them up:

1. Install pre-commit (if not already installed):

```bash
# macOS with Homebrew
brew install pre-commit

# Or with pip
pip install pre-commit
```

2. Install the git hooks:

```bash
pre-commit install
pre-commit install --hook-type commit-msg
```

3. (Optional) Run on all files to check current state:

```bash
pre-commit run --all-files
```

The hooks will now run automatically on each commit, checking for:

- Code formatting (Prettier)
- Linting issues (ESLint)
- Security vulnerabilities (Gitleaks)
- File quality (trailing whitespace, large files, etc.)
- Configuration validation (JSON/YAML syntax)

For detailed information, see [PRE_COMMIT_SETUP.md](./docs/PRE_COMMIT_SETUP.md).

## 📁 Project Structure

```
src/
├── lib/
│   ├── components/
│   │   ├── ui/           # shadcn-svelte components
│   │   └── examples/     # Example components
│   ├── utils.ts          # Utility functions
│   └── index.ts
├── routes/               # SvelteKit routes
├── app.html             # HTML template
└── app.css              # Global styles
```

## 🧩 Component Examples

This project includes several example components demonstrating best practices:

### UserCard Component

- Demonstrates Svelte 5 runes (`$state`, `$derived`)
- Proper TypeScript interfaces
- Accessible design with keyboard navigation
- Loading states and event handling

### TaskManager Component

- Complex state management with arrays
- Filtering and derived state
- Form handling and validation
- CRUD operations

### FormExample Component

- Real-time form validation
- Error handling and display
- Accessibility features
- TypeScript form interfaces

## 🎨 Adding Components

### Using shadcn-svelte CLI

```bash
# Add individual components
npm run shadcn:add button
npm run shadcn:add card
npm run shadcn:add input

# Add multiple components
npx shadcn-svelte@latest add button card input label
```

### Creating Custom Components

1. Create your component in `src/lib/components/`
2. Use TypeScript interfaces for props
3. Implement proper accessibility
4. Follow the established patterns from examples

Example component structure:

```svelte
<script lang="ts" module>
	export interface MyComponentProps {
		// Define your props interface
	}
</script>

<script lang="ts">
	import { cn } from '$lib/utils.js';

	let {
		// Destructure props with defaults
	}: MyComponentProps = $props();

	// Use Svelte 5 runes for state
	let state = $state(initialValue);
	let derived = $derived(computation);
</script>

<!-- Your component template -->
```

## 🧪 Testing

### Unit Tests

```bash
# Run unit tests
npm run test:unit

# Run tests in watch mode
npm run test:unit:watch

# Run tests with UI
npm run test:unit:ui

# Run tests with coverage
npm run test:unit:coverage
```

### End-to-End Tests

```bash
# Run e2e tests
npm run test:e2e

# Run e2e tests with UI
npm run test:e2e:ui
```

## 🔧 Development Scripts

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build

# Performance Analysis
npm run analyze      # Generate bundle analysis
npm run analyze:open # Generate and open bundle analysis
npm run perf:build   # Build + analyze performance

# Code Quality
npm run check        # Type check
npm run check:watch  # Type check in watch mode
npm run lint         # Lint code
npm run lint:fix     # Fix linting issues
npm run format       # Format code
npm run format:check # Check formatting
npm run ci           # Run all checks (CI pipeline)
```

## 📝 Code Quality

This project enforces code quality through:

- **ESLint** for code linting
- **Prettier** for code formatting
- **TypeScript** for type checking
- **svelte-check** for Svelte-specific checks
- **Husky** for git hooks (optional)

## 🎯 Best Practices

### Component Design

- Use TypeScript interfaces for all props
- Implement proper accessibility (ARIA labels, keyboard navigation)
- Use Svelte 5 runes for state management
- Follow the single responsibility principle
- Make components reusable and composable

### State Management

- Use `$state` for reactive variables
- Use `$derived` for computed values
- Use `$effect` for side effects
- Keep state as local as possible
- Use stores for global state when needed

### Styling

- Use TailwindCSS utility classes
- Use the `cn()` utility for conditional classes
- Follow the design system from shadcn-svelte
- Use CSS custom properties for theming

### Performance

This project includes comprehensive performance optimizations:

- **Bundle Analysis**: `npm run analyze` for bundle size analysis
- **Compression**: Gzip + Brotli compression for all assets
- **Lazy Loading**: Optimized image loading with intersection observer
- **Code Splitting**: Intelligent vendor chunk splitting
- **Performance Monitoring**: Real-time Core Web Vitals tracking (dev mode)
- **Performance Budgets**: Build-time warnings for large bundles

See [PERFORMANCE_OPTIMIZATIONS.md](./docs/PERFORMANCE_OPTIMIZATIONS.md) for detailed information.

## 🚀 Deployment

### Build for Production

```bash
npm run build
```

### Deploy to Vercel

```bash
npm i -g vercel
vercel
```

### Deploy to Netlify

```bash
npm run build
# Upload dist folder to Netlify
```

### Deploy to Static Hosting

The project is configured with `@sveltejs/adapter-static` for static site generation.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run the CI checks: `npm run ci`
5. Submit a pull request

## 📚 Documentation

For detailed documentation, see the [docs](./docs/) folder:

- [Performance Optimizations](./docs/PERFORMANCE_OPTIMIZATIONS.md) - Comprehensive performance guide
- [Best Practices](./docs/BEST_PRACTICES.md) - Development guidelines and patterns
- [Pre-commit Setup](./docs/PRE_COMMIT_SETUP.md) - Code quality automation
- [Docker Guide](./docs/DOCKER.md) - Containerization and deployment

## 📚 External Resources

- [Svelte 5 Documentation](https://svelte.dev/docs/svelte/introduction)
- [SvelteKit Documentation](https://kit.svelte.dev/docs)
- [shadcn-svelte Documentation](https://shadcn-svelte.com/docs)
- [TailwindCSS Documentation](https://tailwindcss.com/docs)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)

## 📄 License

MIT License - see the [LICENSE](LICENSE) file for details.
