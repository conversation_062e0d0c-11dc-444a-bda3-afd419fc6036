{"name": "svelte5-shadcn-app", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "format:check": "prettier --check .", "lint": "prettier --check . && eslint .", "lint:fix": "eslint . --fix && prettier --write .", "test:unit": "vitest", "test:unit:ui": "vitest --ui", "test:unit:coverage": "vitest --coverage", "test": "npm run test:unit -- --run && npm run test:e2e", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "ci": "npm run lint && npm run check && npm run test:unit -- --run && npm run build", "analyze": "ANALYZE=true npm run build", "analyze:open": "npm run analyze && open dist/stats.html", "perf:build": "npm run build && npm run analyze", "shadcn:add": "npx shadcn-svelte@latest add", "pre-commit": "pre-commit run --all-files", "pre-commit:install": "pre-commit install && pre-commit install --hook-type commit-msg", "pre-commit:update": "pre-commit autoupdate"}, "devDependencies": {"@biomejs/biome": "^2.1.2", "@eslint/compat": "^1.3.1", "@eslint/js": "^9.31.0", "@internationalized/date": "^3.8.2", "@lucide/svelte": "^0.525.0", "@playwright/test": "^1.54.1", "@sveltejs/adapter-static": "^3.0.8", "@sveltejs/kit": "^2.25.1", "@sveltejs/vite-plugin-svelte": "^6.1.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.11", "@testing-library/jest-dom": "^6.6.3", "@testing-library/svelte": "^5.2.8", "@types/node": "^24.0.15", "@vitest/browser": "^3.2.4", "bits-ui": "^2.8.11", "clsx": "^2.1.1", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-svelte": "^3.11.0", "globals": "^16.3.0", "playwright": "^1.54.1", "prettier": "^3.6.2", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.14", "rollup-plugin-visualizer": "^6.0.3", "svelte": "^5.36.13", "svelte-check": "^4.3.0", "tailwind-merge": "^3.3.1", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.1.11", "terser": "^5.43.1", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3", "typescript-eslint": "^8.38.0", "vite": "^7.0.5", "vite-bundle-analyzer": "^1.1.0", "vite-plugin-compression2": "^2.2.0", "vite-plugin-devtools-json": "^0.3.0", "vitest": "^3.2.4", "vitest-browser-svelte": "^1.0.0"}}