# Documentation Index

This folder contains all the documentation for the svelte5-shadcn-app project.

## 📚 Available Documentation

### 🚀 [Performance Optimizations](./PERFORMANCE_OPTIMIZATIONS.md)

Comprehensive guide to all performance improvements implemented in the project:

- Bundle analysis and monitoring
- Compression and build optimizations
- Image and asset optimizations
- Performance monitoring and Core Web Vitals
- Dependency management

### 📋 [Best Practices](./BEST_PRACTICES.md)

Development best practices and coding standards:

- Svelte 5 patterns and conventions
- Component architecture guidelines
- TypeScript usage patterns
- Testing strategies
- Code organization

### 🔧 [Pre-commit Setup](./PRE_COMMIT_SETUP.md)

Detailed guide for setting up pre-commit hooks:

- Installation instructions
- Configuration options
- Available hooks and their purposes
- Troubleshooting common issues

### 📊 [Pre-commit Summary](./PRE_COMMIT_SUMMARY.md)

Quick reference for pre-commit hooks:

- List of enabled hooks
- What each hook checks
- How to run hooks manually

### 🐳 [Docker Guide](./DOCKER.md)

Docker setup and deployment instructions:

- Development environment setup
- Production deployment
- Docker Compose configurations
- Best practices for containerization

## 🔗 Quick Links

- [Main README](../README.md) - Project overview and getting started
- [Package.json](../package.json) - Dependencies and scripts
- [Vite Config](../vite.config.ts) - Build configuration
- [Svelte Config](../svelte.config.js) - Svelte configuration
- [TypeScript Config](../tsconfig.json) - TypeScript settings

## 📖 External Resources

- [Svelte 5 Documentation](https://svelte.dev/docs/svelte/introduction)
- [SvelteKit Documentation](https://svelte.dev/docs/kit/introduction)
- [shadcn-svelte Documentation](https://shadcn-svelte.com/)
- [TailwindCSS Documentation](https://tailwindcss.com/docs)
- [Vite Documentation](https://vite.dev/)

## 🤝 Contributing to Documentation

When adding new documentation:

1. **Create descriptive filenames** using UPPERCASE for main sections
2. **Update this index** with a brief description
3. **Use consistent formatting** following existing patterns
4. **Include code examples** where applicable
5. **Link to related documentation** for better navigation

## 📝 Documentation Standards

- Use clear, descriptive headings
- Include code examples with syntax highlighting
- Provide step-by-step instructions where needed
- Link to external resources when relevant
- Keep content up-to-date with code changes
