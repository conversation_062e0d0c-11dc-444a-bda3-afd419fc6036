# Docker Setup for Svelte 5 + SvelteKit + shadcn-svelte App

This project includes Docker configurations for both development and production environments.

## Files Overview

- `Dockerfile.dev` - Development environment with hot reloading
- `Dockerfile.prod` - Production environment with optimized build and nginx
- `docker-compose.dev.yml` - Development environment orchestration
- `docker-compose.prod.yml` - Production environment orchestration
- `Makefile` - Convenient commands for Docker operations
- `.dockerignore` - Excludes unnecessary files from Docker context

## Quick Start

### Development Environment

Run the development server with hot reloading:

```bash
# Using Makefile (easiest)
make dev

# Using docker-compose directly
docker-compose -f docker-compose.dev.yml up --build

# Or using Docker directly
docker build -f Dockerfile.dev -t svelte-app-dev .
docker run -p 5173:5173 -v $(pwd):/app -v /app/node_modules svelte-app-dev
```

The development server will be available at http://localhost:5173

### Production Environment

Build and run the production version:

```bash
# Using Makefile (easiest)
make prod

# Using docker-compose directly
docker-compose -f docker-compose.prod.yml up --build -d

# Or using Docker directly
docker build -f Dockerfile.prod -t svelte-app-prod .
docker run -p 8080:80 svelte-app-prod
```

The production app will be available at http://localhost:8080

## Development Features

- **Hot Reloading**: Source code changes are reflected immediately
- **Volume Mounting**: Local files are mounted for development
- **Node Modules Cache**: Prevents reinstalling dependencies on container restart
- **Interactive Terminal**: TTY and stdin support for debugging
- **Isolated Network**: Dedicated development network

## Production Features

- **Multi-stage Build**: Optimized for smaller image size
- **Nginx Server**: Serves static files efficiently
- **Security Headers**: Includes common security headers
- **Gzip Compression**: Reduces bandwidth usage
- **Client-side Routing**: Properly handles SPA routing
- **Asset Caching**: Optimizes static asset delivery
- **Non-root User**: Runs with limited privileges for security
- **Health Checks**: Monitors application health
- **Resource Limits**: Memory constraints for stability
- **Auto Restart**: Restarts on failure unless stopped

## Separate Compose Files

This project uses separate Docker Compose files for development and production:

### Development (`docker-compose.dev.yml`)

- Volume mounting for hot reloading
- Interactive terminal support
- Development-specific environment variables
- Dedicated development network

### Production (`docker-compose.prod.yml`)

- Health checks for monitoring
- Resource limits for stability
- Auto-restart policies
- Production-optimized configuration

### Benefits of Separation

- **Clear Environment Separation**: No confusion between dev and prod configs
- **Optimized Configurations**: Each environment has tailored settings
- **Security**: Production doesn't include development tools or volumes
- **Maintainability**: Easier to manage and update each environment independently

## Makefile Commands

For convenience, use the included Makefile:

```bash
# View all available commands
make help

# Development
make dev          # Build and start development environment
make dev-build    # Build development image only
make dev-up       # Start development services
make dev-down     # Stop development services
make dev-logs     # View development logs

# Production
make prod         # Build and start production environment
make prod-build   # Build production image only
make prod-up      # Start production services (detached)
make prod-down    # Stop production services
make prod-logs    # View production logs

# Cleanup
make clean        # Remove all containers, images, and volumes
```

## Environment Variables

You can customize the behavior using environment variables:

```bash
# Development
NODE_ENV=development
VITE_HOST=0.0.0.0

# Production
NODE_ENV=production
```

## Building Images

### Development Image

```bash
docker build -f Dockerfile.dev -t svelte-app:dev .
```

### Production Image

```bash
docker build -f Dockerfile.prod -t svelte-app:prod .
```

## Troubleshooting

### SvelteKit Static Adapter Issues

If you get "Encountered dynamic routes" error during build:

- The project includes `src/routes/+layout.js` with `export const prerender = true` to enable static generation
- This allows the static adapter to prerender all routes for production deployment

### Port Already in Use

If you get a port conflict, change the port mapping:

```bash
# Change host port (left side)
docker run -p 3000:5173 svelte-app-dev  # Dev
docker run -p 3000:80 svelte-app-prod    # Prod
```

### Permission Issues

If you encounter permission issues with volume mounting in development:

```bash
# On Linux/macOS, ensure proper ownership
sudo chown -R $USER:$USER .
```

### Hot Reloading Not Working

Make sure you're using volume mounting for development:

```bash
docker run -p 5173:5173 -v $(pwd):/app -v /app/node_modules svelte-app-dev
```

### Docker Compose Version Warning

The warning about `version` being obsolete is normal and can be ignored. The compose files have been updated to remove the version field as it's no longer required in modern Docker Compose.

## Performance Tips

1. **Use .dockerignore**: Reduces build context size
2. **Multi-stage builds**: Keeps production images small
3. **Layer caching**: Order Dockerfile commands for optimal caching
4. **Volume mounting**: Use for development only, not production

## Security Considerations

- Both images run as non-root users
- Production includes security headers
- Sensitive files are excluded via .dockerignore
- Environment variables should be used for secrets (not hardcoded)
