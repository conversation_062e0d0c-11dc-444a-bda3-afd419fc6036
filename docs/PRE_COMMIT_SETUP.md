# Pre-commit Setup Guide

This project uses [pre-commit](https://pre-commit.com/) to automatically run code quality checks before each commit. This ensures consistent code quality and catches issues early in the development process.

## What is Pre-commit?

Pre-commit is a framework for managing and maintaining multi-language pre-commit hooks. It runs various checks on your code before you commit, helping to:

- Maintain consistent code formatting
- Catch common errors and security issues
- Enforce coding standards
- Validate configuration files
- Prevent committing sensitive information

## Installation

### Prerequisites

- Python 3.7+ (for pre-commit framework)
- Node.js 18+ (for local hooks like ESLint and <PERSON><PERSON><PERSON>)

### Install Pre-commit

Pre-commit is already installed via Homebrew on this system. If you need to install it elsewhere:

```bash
# Using Homebrew (macOS)
brew install pre-commit

# Using pip
pip install pre-commit

# Using conda
conda install -c conda-forge pre-commit
```

### Install Git Hooks

After cloning the repository, run:

```bash
pre-commit install
pre-commit install --hook-type commit-msg
```

This installs the git hooks that will run automatically on each commit.

## Configured Hooks

### Core Quality Hooks (pre-commit/pre-commit-hooks)

- **trailing-whitespace**: Removes trailing whitespace from files
- **end-of-file-fixer**: Ensures files end with a newline
- **check-yaml**: Validates YAML file syntax
- **check-json**: Validates JSON file syntax
- **check-toml**: Validates TOML file syntax
- **check-merge-conflict**: Checks for merge conflict markers
- **check-added-large-files**: Prevents large files from being committed (>1MB)
- **mixed-line-ending**: Ensures consistent line endings (LF)
- **check-case-conflict**: Checks for case conflicts in filenames
- **check-symlinks**: Checks for broken symlinks

### Security Hooks

- **gitleaks**: Detects secrets, passwords, and API keys in code
- **typos**: Finds and fixes common typographical errors

### Configuration Validation

- **check-jsonschema**: Validates package.json and tsconfig.json against their schemas

### Local Project Hooks

- **eslint**: Lints JavaScript/TypeScript/Svelte files with auto-fix
- **prettier**: Formats code according to project style guidelines

## Usage

### Automatic Execution

Pre-commit hooks run automatically when you commit:

```bash
git add .
git commit -m "Your commit message"
# Hooks will run automatically
```

If any hook fails, the commit will be blocked until you fix the issues.

### Manual Execution

Run all hooks on all files:

```bash
pre-commit run --all-files
```

Run a specific hook:

```bash
pre-commit run eslint
pre-commit run prettier
pre-commit run gitleaks
```

Run hooks on specific files:

```bash
pre-commit run --files src/lib/utils.ts
```

### Skipping Hooks

If you need to skip hooks for a specific commit (not recommended):

```bash
git commit -m "Your message" --no-verify
```

Skip specific hooks:

```bash
SKIP=eslint git commit -m "Your message"
SKIP=eslint,prettier git commit -m "Your message"
```

## Configuration Files

### .pre-commit-config.yaml

Main configuration file defining all hooks and their settings.

### .typos.toml

Configuration for the typos checker, including project-specific words that should not be flagged.

### biome.json

Configuration for Biome (alternative to ESLint + Prettier), currently available but not active in pre-commit.

## Troubleshooting

### Hook Installation Issues

If hooks fail to install:

```bash
pre-commit clean
pre-commit install
```

### Node.js Environment Issues

Some hooks may have Node.js environment issues. If you encounter problems:

1. Ensure Node.js 18+ is installed
2. Try using local hooks instead of remote ones
3. Check the pre-commit log: `cat ~/.cache/pre-commit/pre-commit.log`

### ESLint Errors

If ESLint reports errors:

1. Fix the errors manually
2. Run `npm run lint:fix` to auto-fix what's possible
3. For Svelte-specific issues, check the Svelte ESLint plugin documentation

### Performance

First run may be slow as environments are set up. Subsequent runs are much faster due to caching.

## Updating Hooks

Update to latest versions:

```bash
pre-commit autoupdate
```

This updates the `rev` fields in `.pre-commit-config.yaml` to the latest tags.

## Integration with CI/CD

The same hooks can run in CI/CD pipelines:

```bash
pre-commit run --all-files
```

This ensures the same quality checks run locally and in CI.

## Best Practices

1. **Run hooks before pushing**: `pre-commit run --all-files`
2. **Keep hooks updated**: Run `pre-commit autoupdate` regularly
3. **Don't skip hooks**: Only use `--no-verify` in exceptional cases
4. **Fix issues promptly**: Don't let linting errors accumulate
5. **Customize for your project**: Adjust configurations as needed

## Getting Help

- Pre-commit documentation: https://pre-commit.com/
- ESLint documentation: https://eslint.org/
- Prettier documentation: https://prettier.io/
- Gitleaks documentation: https://github.com/gitleaks/gitleaks

For project-specific issues, check the existing configurations and adjust as needed.
