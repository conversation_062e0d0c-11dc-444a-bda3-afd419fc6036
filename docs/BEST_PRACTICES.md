# Best Practices Guide

This document outlines the best practices for building maintainable components with Svelte 5, SvelteKit, and shadcn-svelte.

## 🏗️ Component Architecture

### 1. Component Structure

Follow this consistent structure for all components:

```svelte
<script lang="ts" module>
	// Type definitions and interfaces
	export interface ComponentProps {
		// Define all props with proper types
	}
</script>

<script lang="ts">
	// Imports
	import { cn } from '$lib/utils.js';
	import { ComponentName } from '$lib/components/ui/component/index.js';

	// Props destructuring with defaults
	let {
		prop1,
		prop2 = 'default',
		class: className,
		children,
		...restProps
	}: ComponentProps = $props();

	// State using Svelte 5 runes
	let localState = $state(initialValue);

	// Derived state
	let computedValue = $derived(computation);

	// Effects
	$effect(() => {
		// Side effects
	});

	// Event handlers
	function handleEvent() {
		// Event logic
	}
</script>

<!-- Template -->
<div class={cn('base-classes', className)} {...restProps}>
	{@render children?.()}
</div>
```

### 2. TypeScript Best Practices

#### Define Clear Interfaces

```typescript
// Good: Specific, well-documented interface
export interface UserCardProps extends HTMLAttributes<HTMLDivElement> {
	/** User data to display */
	user: User;
	/** Callback when user is clicked */
	onUserClick?: (user: User) => void;
	/** Show online status indicator */
	showStatus?: boolean;
}

// Avoid: Vague or missing types
export interface Props {
	data: any;
	callback?: Function;
}
```

#### Use Proper Event Types

```typescript
// Good: Specific event types
function handleClick(event: MouseEvent) {
	// Handle click
}

function handleInput(event: Event & { currentTarget: HTMLInputElement }) {
	const value = event.currentTarget.value;
}

// Better: Use Svelte's event types
function handleSubmit(event: SubmitEvent) {
	event.preventDefault();
}
```

### 3. State Management with Runes

#### Use $state for Reactive Variables

```typescript
// Good: Simple reactive state
let count = $state(0);
let user = $state<User | null>(null);
let items = $state<Item[]>([]);

// Good: Complex state objects
let formData = $state({
	name: '',
	email: '',
	message: ''
});
```

#### Use $derived for Computed Values

```typescript
// Good: Derived from other state
let isValid = $derived(formData.name && formData.email);
let filteredItems = $derived.by(() => items.filter((item) => item.category === selectedCategory));

// Avoid: Manual updates
let isValid = $state(false);
// Manually updating isValid when formData changes
```

#### Use $effect for Side Effects

```typescript
// Good: Proper effect usage
$effect(() => {
	if (user) {
		document.title = `Welcome, ${user.name}`;
	}
});

// Good: Cleanup in effects
$effect(() => {
	const interval = setInterval(() => {
		// Do something
	}, 1000);

	return () => clearInterval(interval);
});
```

## 🎨 Styling Best Practices

### 1. TailwindCSS Usage

#### Use Semantic Class Names

```svelte
<!-- Good: Semantic and maintainable -->
<button class="btn-primary"> Click me </button>

<!-- Also good: Direct utilities for simple cases -->
<div class="flex items-center space-x-2">
	<!-- Simple layout utilities -->
</div>

<style>
	.btn-primary {
		@apply rounded-md bg-blue-500 px-4 py-2 font-medium text-white transition-colors hover:bg-blue-600;
	}
</style>
```

#### Use the cn() Utility

```svelte
<script lang="ts">
	import { cn } from '$lib/utils.js';

	let { variant = 'default', size = 'md', class: className } = $props();
</script>

<button
	class={cn(
		'base-button-classes',
		{
			'variant-primary': variant === 'primary',
			'variant-secondary': variant === 'secondary',
			'size-sm': size === 'sm',
			'size-lg': size === 'lg'
		},
		className
	)}
>
	<!-- Button content -->
</button>
```

### 2. Component Variants

Use a consistent pattern for component variants:

```typescript
// Define variant types
export type ButtonVariant = 'default' | 'primary' | 'secondary' | 'destructive';
export type ButtonSize = 'sm' | 'md' | 'lg';

export interface ButtonProps {
	variant?: ButtonVariant;
	size?: ButtonSize;
	disabled?: boolean;
}
```

## ♿ Accessibility Best Practices

### 1. Semantic HTML

```svelte
<!-- Good: Semantic elements -->
<nav aria-label="Main navigation">
	<ul>
		<li><a href="/">Home</a></li>
		<li><a href="/about">About</a></li>
	</ul>
</nav>

<main>
	<h1>Page Title</h1>
	<article>
		<h2>Article Title</h2>
		<p>Article content...</p>
	</article>
</main>
```

### 2. ARIA Labels and Roles

```svelte
<!-- Good: Proper ARIA attributes -->
<button aria-label="Close dialog" aria-expanded={isOpen} onclick={toggleDialog}>
	<CloseIcon aria-hidden="true" />
</button>

<div role="tabpanel" aria-labelledby="tab-1" tabindex="0">
	<!-- Tab content -->
</div>
```

### 3. Keyboard Navigation

```svelte
<script lang="ts">
	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			handleClick();
		}
	}
</script>

<div role="button" tabindex="0" onkeydown={handleKeydown} onclick={handleClick}>Clickable div</div>
```

## 🔄 State Management Patterns

### 1. Local State

Keep state as local as possible:

```svelte
<!-- Good: Local state for component-specific data -->
<script lang="ts">
	let isLoading = $state(false);
	let error = $state<string | null>(null);

	async function handleSubmit() {
		isLoading = true;
		error = null;

		try {
			await submitForm();
		} catch (err) {
			error = err.message;
		} finally {
			isLoading = false;
		}
	}
</script>
```

### 2. Shared State with Stores

For global state, use Svelte stores:

```typescript
// stores.ts
import { writable } from 'svelte/store';

export const user = writable<User | null>(null);
export const theme = writable<'light' | 'dark'>('light');

// In components
import { user } from '$lib/stores.js';

let currentUser = $state($user);
```

## 🧪 Testing Best Practices

### 1. Component Testing

```typescript
// UserCard.test.ts
import { render, screen } from '@testing-library/svelte';
import { expect, test } from 'vitest';
import UserCard from './UserCard.svelte';

test('renders user information', () => {
	const user = {
		id: 1,
		name: 'John Doe',
		email: '<EMAIL>',
		role: 'Developer'
	};

	render(UserCard, { user });

	expect(screen.getByText('John Doe')).toBeInTheDocument();
	expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
});
```

### 2. Testing User Interactions

```typescript
import { fireEvent } from '@testing-library/svelte';

test('calls onUserClick when clicked', async () => {
	const onUserClick = vi.fn();
	const user = {
		/* user data */
	};

	render(UserCard, { user, onUserClick });

	await fireEvent.click(screen.getByRole('button'));

	expect(onUserClick).toHaveBeenCalledWith(user);
});
```

## 🚀 Performance Best Practices

### 1. Optimize Reactivity

```svelte
<!-- Good: Specific dependencies -->
{#each filteredItems as item (item.id)}
	<ItemCard {item} />
{/each}

<!-- Avoid: Unnecessary reactivity -->
{#each items.filter((item) => item.visible) as item}
	<!-- This filters on every render -->
{/each}
```

### 2. Use Key Blocks When Needed

```svelte
<!-- Good: Use key for expensive components -->
{#key selectedUser.id}
	<ExpensiveUserProfile user={selectedUser} />
{/key}
```

### 3. Lazy Loading

```svelte
<script lang="ts">
	import { onMount } from 'svelte';

	let LazyComponent: any = null;

	onMount(async () => {
		const module = await import('./LazyComponent.svelte');
		LazyComponent = module.default;
	});
</script>

{#if LazyComponent}
	<svelte:component this={LazyComponent} />
{/if}
```

## 📁 File Organization

### 1. Directory Structure

```
src/lib/components/
├── ui/                 # shadcn-svelte components
│   ├── button/
│   ├── card/
│   └── input/
├── layout/            # Layout components
│   ├── Header.svelte
│   ├── Footer.svelte
│   └── Sidebar.svelte
├── features/          # Feature-specific components
│   ├── auth/
│   ├── dashboard/
│   └── profile/
└── shared/           # Shared utility components
    ├── LoadingSpinner.svelte
    ├── ErrorBoundary.svelte
    └── Modal.svelte
```

### 2. Naming Conventions

- **Components**: PascalCase (`UserCard.svelte`)
- **Files**: kebab-case for non-components (`user-utils.ts`)
- **Props**: camelCase (`isLoading`, `onUserClick`)
- **CSS classes**: kebab-case (`btn-primary`, `user-card`)

## 🔧 Development Workflow

### 1. Pre-commit Checks

```json
{
	"scripts": {
		"pre-commit": "npm run lint:fix && npm run format && npm run check"
	}
}
```

### 2. Component Development Checklist

- [ ] TypeScript interfaces defined
- [ ] Accessibility implemented
- [ ] Responsive design tested
- [ ] Error states handled
- [ ] Loading states implemented
- [ ] Unit tests written
- [ ] Documentation updated

This guide should be followed consistently across all components to ensure maintainability and code quality.
