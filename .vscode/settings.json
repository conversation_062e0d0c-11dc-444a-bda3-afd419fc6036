{"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "svelte.enable-ts-plugin": true, "typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "emmet.includeLanguages": {"svelte": "html"}, "files.associations": {"*.svelte": "svelte"}, "tailwindCSS.includeLanguages": {"svelte": "html"}, "tailwindCSS.experimental.classRegex": [["cn\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"]]}