# Configuration for typos spell checker
# See https://github.com/crate-ci/typos for more information

[files]
# Extend the default list of files to check
extend-exclude = [
  "node_modules/",
  ".svelte-kit/",
  "build/",
  "dist/",
  "coverage/",
  "*.min.js",
  "*.min.css",
  "package-lock.json",
  "*.svg",
  "*.png",
  "*.jpg",
  "*.jpeg",
  "*.gif",
  "*.ico",
  "*.woff",
  "*.woff2",
  "*.ttf",
  "*.eot"
]

[default]
# Check file names and identifiers
check-filenames = true
check-files = true

# Locale for spell checking
locale = "en-us"

[default.extend-words]
# Add project-specific words that should not be flagged as typos
# Svelte/SvelteKit specific terms
svelte = "svelte"
sveltekit = "sveltekit"
vite = "vite"
vitest = "vitest"
playwright = "playwright"

# UI/CSS framework terms
tailwindcss = "tailwindcss"
shadcn = "shadcn"
lucide = "lucide"

# Technical terms
typescript = "typescript"
javascript = "javascript"
nodejs = "nodejs"
npm = "npm"
eslint = "eslint"
prettier = "prettier"
biome = "biome"

# Common abbreviations and technical terms
config = "config"
configs = "configs"
repo = "repo"
repos = "repos"
dev = "dev"
prod = "prod"
env = "env"
cli = "cli"
api = "api"
url = "url"
urls = "urls"
uuid = "uuid"
uuids = "uuids"

[type.md]
# Markdown specific configuration
extend-glob = ["*.md", "*.mdx"]

[type.json]
# JSON specific configuration
extend-glob = ["*.json", "*.jsonc"]

[type.yaml]
# YAML specific configuration
extend-glob = ["*.yaml", "*.yml"]
