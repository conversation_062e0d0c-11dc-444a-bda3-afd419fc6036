<script lang="ts">
	import FormExample from '$lib/components/examples/FormExample.svelte';
	import TaskManager from '$lib/components/examples/TaskManager.svelte';
	import UserCard from '$lib/components/examples/UserCard.svelte';
	import { Button } from '$lib/components/ui/button/index.js';

	// Example data for demonstration
	const users = [
		{
			id: 1,
			name: '<PERSON>',
			email: '<EMAIL>',
			avatar:
				'https://api.dicebear.com/7.x/avataaars/svg?seed=Alice&backgroundColor=b6e3f4&clothesColor=262e33&eyebrowType=default&eyeType=happy&facialHairColor=auburn&facialHairType=blank&hairColor=auburn&hatColor=black&mouthType=smile&skinColor=light&topType=longHairStraight',
			role: 'Frontend Developer',
			isOnline: true
		},
		{
			id: 2,
			name: '<PERSON>',
			email: '<EMAIL>',
			avatar:
				'https://api.dicebear.com/7.x/avataaars/svg?seed=Bob&backgroundColor=c0aede&clothesColor=3c4f5c&eyebrowType=default&eyeType=default&facialHairColor=brown&facialHairType=moustacheFancy&hairColor=brown&hatColor=black&mouthType=default&skinColor=light&topType=shortHairShortFlat',
			role: 'Backend Developer',
			isOnline: false
		},
		{
			id: 3,
			name: '<PERSON> <PERSON>',
			email: '<EMAIL>',
			avatar:
				'https://api.dicebear.com/7.x/avataaars/svg?seed=Carol&backgroundColor=ffd93d&clothesColor=ff488e&eyebrowType=default&eyeType=happy&facialHairColor=blonde&facialHairType=blank&hairColor=blonde&hatColor=black&mouthType=smile&skinColor=light&topType=longHairCurly',
			role: 'UI/UX Designer',
			isOnline: true
		}
	];
</script>

<div class="container mx-auto space-y-12 p-8">
	<header class="space-y-4 text-center">
		<h1 class="text-4xl font-bold text-gray-900 dark:text-gray-100">
			Svelte 5 + SvelteKit + shadcn-svelte
		</h1>
		<p class="mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-400">
			A maintainable frontend component library built with the latest technologies and best
			practices.
		</p>
		<div class="flex justify-center gap-4">
			<Button variant="default">Get Started</Button>
			<Button variant="outline">View Documentation</Button>
		</div>
	</header>

	<section class="space-y-6">
		<h2 class="text-2xl font-semibold text-gray-900 dark:text-gray-100">Example Components</h2>

		<!-- User Cards Example -->
		<div class="space-y-4">
			<h3 class="text-xl font-medium text-gray-800 dark:text-gray-200">User Cards</h3>
			<div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
				{#each users as user (user.id)}
					<UserCard {user} />
				{/each}
			</div>
		</div>

		<!-- Task Manager Example -->
		<div class="space-y-4">
			<h3 class="text-xl font-medium text-gray-800 dark:text-gray-200">Task Manager</h3>
			<TaskManager />
		</div>

		<!-- Form Example -->
		<div class="space-y-4">
			<h3 class="text-xl font-medium text-gray-800 dark:text-gray-200">Form Example</h3>
			<FormExample />
		</div>
	</section>
</div>
