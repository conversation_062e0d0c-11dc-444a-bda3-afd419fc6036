import { expect, test } from 'vitest';
import { cn } from './utils.js';

test('cn combines class names correctly', () => {
	expect(cn('class1', 'class2')).toBe('class1 class2');
});

test('cn handles conditional classes', () => {
	expect(cn('base', { conditional: true, hidden: false })).toBe('base conditional');
});

test('cn merges Tailwind classes correctly', () => {
	// Should merge conflicting Tailwind classes, keeping the last one
	expect(cn('p-2', 'p-4')).toBe('p-4');
	expect(cn('text-red-500', 'text-blue-500')).toBe('text-blue-500');
});

test('cn handles arrays of classes', () => {
	expect(cn(['class1', 'class2'], 'class3')).toBe('class1 class2 class3');
});

test('cn filters out falsy values', () => {
	expect(cn('class1', null, undefined, false, '', 'class2')).toBe('class1 class2');
});

test('cn handles complex combinations', () => {
	const result = cn(
		'base-class',
		{
			active: true,
			disabled: false
		},
		['array-class1', 'array-class2'],
		'final-class'
	);

	expect(result).toBe('base-class active array-class1 array-class2 final-class');
});
