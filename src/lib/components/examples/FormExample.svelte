<script lang="ts" module>
	export interface FormData {
		name: string;
		email: string;
		message: string;
		category: string;
		newsletter: boolean;
	}

	export interface FormErrors {
		name?: string;
		email?: string;
		message?: string;
		category?: string;
	}
</script>

<script lang="ts">
	import { Button } from '$lib/components/ui/button/index.js';
	import { cn } from '$lib/utils.js';

	// Form state using Svelte 5 runes
	let formData = $state<FormData>({
		name: '',
		email: '',
		message: '',
		category: '',
		newsletter: false
	});

	let errors = $state<FormErrors>({});
	let isSubmitting = $state(false);
	let submitSuccess = $state(false);
	let touched = $state<Record<keyof FormData, boolean>>({
		name: false,
		email: false,
		message: false,
		category: false,
		newsletter: false
	});

	// Validation functions
	function validateEmail(email: string): boolean {
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		return emailRegex.test(email);
	}

	function validateForm(): FormErrors {
		const newErrors: FormErrors = {};

		if (!formData.name.trim()) {
			newErrors.name = 'Name is required';
		} else if (formData.name.trim().length < 2) {
			newErrors.name = 'Name must be at least 2 characters';
		}

		if (!formData.email.trim()) {
			newErrors.email = 'Email is required';
		} else if (!validateEmail(formData.email)) {
			newErrors.email = 'Please enter a valid email address';
		}

		if (!formData.message.trim()) {
			newErrors.message = 'Message is required';
		} else if (formData.message.trim().length < 10) {
			newErrors.message = 'Message must be at least 10 characters';
		}

		if (!formData.category) {
			newErrors.category = 'Please select a category';
		}

		return newErrors;
	}

	// Derived state for real-time validation
	let isFormValid = $derived(() => {
		const currentErrors = validateForm();
		return Object.keys(currentErrors).length === 0;
	});

	// Event handlers
	function handleFieldBlur(field: keyof FormData) {
		touched[field] = true;
		errors = validateForm();
	}

	function handleInput<K extends keyof FormData>(field: K, value: FormData[K]) {
		formData[field] = value;

		// Clear error for this field if it becomes valid
		if (touched[field] && field !== 'newsletter') {
			const fieldErrors = validateForm();
			if (!fieldErrors[field as keyof FormErrors]) {
				delete errors[field as keyof FormErrors];
			} else {
				errors[field as keyof FormErrors] = fieldErrors[field as keyof FormErrors];
			}
		}
	}

	async function handleSubmit(event: Event) {
		event.preventDefault();

		// Mark all fields as touched
		Object.keys(touched).forEach((key) => {
			touched[key as keyof FormData] = true;
		});

		// Validate form
		const formErrors = validateForm();
		errors = formErrors;

		if (Object.keys(formErrors).length > 0) {
			return;
		}

		isSubmitting = true;

		try {
			// Simulate API call
			await new Promise((resolve) => setTimeout(resolve, 2000));

			console.log('Form submitted:', formData);
			submitSuccess = true;

			// Reset form after successful submission
			setTimeout(() => {
				formData = {
					name: '',
					email: '',
					message: '',
					category: '',
					newsletter: false
				};
				errors = {};
				touched = {
					name: false,
					email: false,
					message: false,
					category: false,
					newsletter: false
				};
				submitSuccess = false;
			}, 3000);
		} catch (error) {
			console.error('Form submission error:', error);
		} finally {
			isSubmitting = false;
		}
	}

	function getFieldError(field: keyof FormData): string | undefined {
		return touched[field] && field !== 'newsletter' ? errors[field as keyof FormErrors] : undefined;
	}
</script>

<div
	class="max-w-2xl rounded-lg border border-gray-200 bg-white p-6 shadow-md dark:border-gray-700 dark:bg-gray-800"
>
	<h2 class="mb-6 text-xl font-semibold text-gray-900 dark:text-gray-100">Contact Form</h2>

	{#if submitSuccess}
		<div
			class="mb-6 rounded-lg border border-green-200 bg-green-50 p-4 dark:border-green-800 dark:bg-green-900/20"
		>
			<div class="flex items-center">
				<svg
					class="mr-2 h-5 w-5 text-green-600 dark:text-green-400"
					fill="currentColor"
					viewBox="0 0 20 20"
				>
					<path
						fill-rule="evenodd"
						d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
						clip-rule="evenodd"
					></path>
				</svg>
				<p class="font-medium text-green-800 dark:text-green-200">
					Thank you! Your message has been sent successfully.
				</p>
			</div>
		</div>
	{/if}

	<form onsubmit={handleSubmit} class="space-y-6">
		<!-- Name Field -->
		<div>
			<label for="name" class="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
				Name *
			</label>
			<input
				id="name"
				type="text"
				value={formData.name}
				oninput={(e) => handleInput('name', e.currentTarget.value)}
				onblur={() => handleFieldBlur('name')}
				placeholder="Enter your full name"
				class={cn(
					'w-full rounded-md border px-3 py-2 transition-colors focus:ring-2 focus:outline-none',
					getFieldError('name')
						? 'border-red-500 focus:ring-red-500 dark:border-red-400'
						: 'border-gray-300 focus:ring-blue-500 dark:border-gray-600',
					'dark:bg-gray-700 dark:text-gray-100'
				)}
			/>
			{#if getFieldError('name')}
				<p class="mt-1 text-sm text-red-600 dark:text-red-400">{getFieldError('name')}</p>
			{/if}
		</div>

		<!-- Email Field -->
		<div>
			<label for="email" class="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
				Email *
			</label>
			<input
				id="email"
				type="email"
				value={formData.email}
				oninput={(e) => handleInput('email', e.currentTarget.value)}
				onblur={() => handleFieldBlur('email')}
				placeholder="Enter your email address"
				class={cn(
					'w-full rounded-md border px-3 py-2 transition-colors focus:ring-2 focus:outline-none',
					getFieldError('email')
						? 'border-red-500 focus:ring-red-500 dark:border-red-400'
						: 'border-gray-300 focus:ring-blue-500 dark:border-gray-600',
					'dark:bg-gray-700 dark:text-gray-100'
				)}
			/>
			{#if getFieldError('email')}
				<p class="mt-1 text-sm text-red-600 dark:text-red-400">{getFieldError('email')}</p>
			{/if}
		</div>

		<!-- Category Field -->
		<div>
			<label for="category" class="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
				Category *
			</label>
			<select
				id="category"
				value={formData.category}
				onchange={(e) => handleInput('category', e.currentTarget.value)}
				onblur={() => handleFieldBlur('category')}
				class={cn(
					'w-full rounded-md border px-3 py-2 transition-colors focus:ring-2 focus:outline-none',
					getFieldError('category')
						? 'border-red-500 focus:ring-red-500 dark:border-red-400'
						: 'border-gray-300 focus:ring-blue-500 dark:border-gray-600',
					'dark:bg-gray-700 dark:text-gray-100'
				)}
			>
				<option value="">Select a category</option>
				<option value="general">General Inquiry</option>
				<option value="support">Technical Support</option>
				<option value="feedback">Feedback</option>
				<option value="business">Business Inquiry</option>
			</select>
			{#if getFieldError('category')}
				<p class="mt-1 text-sm text-red-600 dark:text-red-400">{getFieldError('category')}</p>
			{/if}
		</div>

		<!-- Message Field -->
		<div>
			<label for="message" class="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
				Message *
			</label>
			<textarea
				id="message"
				value={formData.message}
				oninput={(e) => handleInput('message', e.currentTarget.value)}
				onblur={() => handleFieldBlur('message')}
				placeholder="Enter your message (minimum 10 characters)"
				rows="4"
				class={cn(
					'resize-vertical w-full rounded-md border px-3 py-2 transition-colors focus:ring-2 focus:outline-none',
					getFieldError('message')
						? 'border-red-500 focus:ring-red-500 dark:border-red-400'
						: 'border-gray-300 focus:ring-blue-500 dark:border-gray-600',
					'dark:bg-gray-700 dark:text-gray-100'
				)}
			></textarea>
			<div class="mt-1 flex items-center justify-between">
				{#if getFieldError('message')}
					<p class="text-sm text-red-600 dark:text-red-400">{getFieldError('message')}</p>
				{:else}
					<div></div>
				{/if}
				<p class="text-sm text-gray-500 dark:text-gray-400">
					{formData.message.length} characters
				</p>
			</div>
		</div>

		<!-- Newsletter Checkbox -->
		<div class="flex items-center">
			<input
				id="newsletter"
				type="checkbox"
				checked={formData.newsletter}
				onchange={(e) => handleInput('newsletter', e.currentTarget.checked)}
				class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
			/>
			<label for="newsletter" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
				Subscribe to our newsletter for updates
			</label>
		</div>

		<!-- Submit Button -->
		<div class="flex space-x-4">
			<Button
				type="submit"
				variant="default"
				disabled={!isFormValid || isSubmitting}
				class="flex-1"
			>
				{isSubmitting ? 'Sending...' : 'Send Message'}
			</Button>

			<Button
				type="button"
				variant="outline"
				onclick={() => {
					formData = {
						name: '',
						email: '',
						message: '',
						category: '',
						newsletter: false
					};
					errors = {};
					touched = {
						name: false,
						email: false,
						message: false,
						category: false,
						newsletter: false
					};
				}}
			>
				Reset
			</Button>
		</div>
	</form>
</div>
