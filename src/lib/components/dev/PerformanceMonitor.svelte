<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { PerformanceMonitor, type PerformanceMetric } from '$lib/utils/performance.js';

	let monitor: PerformanceMonitor | null = null;
	let metrics = $state<PerformanceMetric[]>([]);
	let isVisible = $state(false);

	onMount(() => {
		// Only enable in development
		if (!import.meta.env.DEV) return;

		monitor = new PerformanceMonitor((metric) => {
			metrics = [...metrics, metric];
		});

		// Show monitor if there are performance issues
		const checkPerformance = () => {
			const hasIssues = metrics.some((m) => m.rating === 'poor');
			if (hasIssues && !isVisible) {
				isVisible = true;
			}
		};

		const interval = setInterval(checkPerformance, 5000);

		return () => {
			clearInterval(interval);
		};
	});

	onDestroy(() => {
		monitor?.disconnect();
	});

	function getRatingColor(rating: string): string {
		switch (rating) {
			case 'good':
				return 'text-green-600 bg-green-100';
			case 'needs-improvement':
				return 'text-yellow-600 bg-yellow-100';
			case 'poor':
				return 'text-red-600 bg-red-100';
			default:
				return 'text-gray-600 bg-gray-100';
		}
	}

	function formatValue(metric: PerformanceMetric): string {
		if (metric.name === 'CLS') {
			return metric.value.toFixed(3);
		}
		return `${metric.value.toFixed(0)}ms`;
	}
</script>

{#if import.meta.env.DEV && isVisible}
	<div
		class="fixed right-4 bottom-4 z-50 max-w-sm rounded-lg border bg-white p-4 shadow-lg dark:bg-gray-800"
	>
		<div class="mb-2 flex items-center justify-between">
			<h3 class="text-sm font-semibold text-gray-900 dark:text-gray-100">Performance Monitor</h3>
			<button
				onclick={() => (isVisible = false)}
				class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
				aria-label="Close performance monitor"
			>
				<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M6 18L18 6M6 6l12 12"
					></path>
				</svg>
			</button>
		</div>

		<div class="space-y-2">
			{#each metrics as metric (metric.name + metric.timestamp)}
				<div class="flex items-center justify-between text-xs">
					<span class="font-medium text-gray-700 dark:text-gray-300">
						{metric.name}
					</span>
					<div class="flex items-center space-x-2">
						<span class="text-gray-600 dark:text-gray-400">
							{formatValue(metric)}
						</span>
						<span
							class={`rounded px-1.5 py-0.5 text-xs font-medium ${getRatingColor(metric.rating)}`}
						>
							{metric.rating === 'needs-improvement' ? 'needs work' : metric.rating}
						</span>
					</div>
				</div>
			{/each}

			{#if metrics.length === 0}
				<p class="text-xs text-gray-500 dark:text-gray-400">Collecting performance metrics...</p>
			{/if}
		</div>

		<div class="mt-3 text-xs text-gray-500 dark:text-gray-400">
			<p>Core Web Vitals monitoring active</p>
		</div>
	</div>
{/if}
