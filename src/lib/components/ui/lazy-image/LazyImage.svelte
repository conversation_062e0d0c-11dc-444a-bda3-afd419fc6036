<script lang="ts">
	import { onMount } from 'svelte';
	import { cn } from '$lib/utils.js';

	interface LazyImageProps {
		src: string;
		alt: string;
		class?: string;
		width?: number;
		height?: number;
		placeholder?: string;
		loading?: 'lazy' | 'eager';
		decoding?: 'async' | 'sync' | 'auto';
		sizes?: string;
		srcset?: string;
		onLoad?: () => void;
		onError?: () => void;
	}

	let {
		src,
		alt,
		class: className,
		width,
		height,
		placeholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzlmYTJhOCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkxvYWRpbmcuLi48L3RleHQ+PC9zdmc+',
		loading = 'lazy',
		decoding = 'async',
		sizes,
		srcset,
		onLoad,
		onError,
		...restProps
	}: LazyImageProps = $props();

	let imageElement: HTMLImageElement;
	let isLoaded = $state(false);
	let hasError = $state(false);
	let isIntersecting = $state(false);

	// Intersection Observer for lazy loading
	onMount(() => {
		if (!imageElement || loading === 'eager') {
			isIntersecting = true;
			return;
		}

		const observer = new IntersectionObserver(
			(entries) => {
				entries.forEach((entry) => {
					if (entry.isIntersecting) {
						isIntersecting = true;
						observer.unobserve(entry.target);
					}
				});
			},
			{
				rootMargin: '50px' // Start loading 50px before the image enters viewport
			}
		);

		observer.observe(imageElement);

		return () => {
			observer.disconnect();
		};
	});

	function handleLoad() {
		isLoaded = true;
		onLoad?.();
	}

	function handleError() {
		hasError = true;
		onError?.();
	}

	// Determine which src to use
	let imageSrc = $derived(isIntersecting || loading === 'eager' ? src : placeholder);
</script>

<img
	bind:this={imageElement}
	src={imageSrc}
	{alt}
	{width}
	{height}
	{sizes}
	{srcset}
	{loading}
	{decoding}
	class={cn(
		'transition-opacity duration-300',
		isLoaded && !hasError ? 'opacity-100' : 'opacity-70',
		hasError && 'bg-gray-200 dark:bg-gray-700',
		className
	)}
	onload={handleLoad}
	onerror={handleError}
	{...restProps}
/>

{#if hasError}
	<div
		class={cn(
			'flex items-center justify-center bg-gray-200 text-gray-500 dark:bg-gray-700 dark:text-gray-400',
			className
		)}
		style={width && height ? `width: ${width}px; height: ${height}px;` : ''}
	>
		<svg
			class="h-8 w-8"
			fill="none"
			stroke="currentColor"
			viewBox="0 0 24 24"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				stroke-linecap="round"
				stroke-linejoin="round"
				stroke-width="2"
				d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
			></path>
		</svg>
	</div>
{/if}
