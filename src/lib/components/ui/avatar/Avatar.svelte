<script lang="ts">
	import { LazyImage } from '$lib/components/ui/lazy-image/index.js';
	import { cn } from '$lib/utils.js';

	interface AvatarProps {
		src?: string;
		alt: string;
		name?: string;
		size?: 'sm' | 'md' | 'lg' | 'xl';
		class?: string;
		loading?: 'lazy' | 'eager';
		fallbackColor?: string;
	}

	let {
		src,
		alt,
		name,
		size = 'md',
		class: className,
		loading = 'lazy',
		fallbackColor,
		...restProps
	}: AvatarProps = $props();

	let hasError = $state(false);

	// Size configurations
	const sizeConfig = {
		sm: { size: 'h-8 w-8', text: 'text-xs', pixels: 32 },
		md: { size: 'h-12 w-12', text: 'text-lg', pixels: 48 },
		lg: { size: 'h-16 w-16', text: 'text-xl', pixels: 64 },
		xl: { size: 'h-20 w-20', text: 'text-2xl', pixels: 80 }
	};

	let config = $derived(sizeConfig[size]);
	let initials = $derived(name ? name.charAt(0).toUpperCase() : alt.charAt(0).toUpperCase());

	// Generate a consistent color based on the name/alt
	let avatarColor = $derived(fallbackColor || generateAvatarColor(name || alt));

	function generateAvatarColor(text: string): string {
		const colors = [
			'from-blue-400 to-blue-600',
			'from-green-400 to-green-600',
			'from-purple-400 to-purple-600',
			'from-pink-400 to-pink-600',
			'from-indigo-400 to-indigo-600',
			'from-red-400 to-red-600',
			'from-yellow-400 to-yellow-600',
			'from-teal-400 to-teal-600'
		];

		let hash = 0;
		for (let i = 0; i < text.length; i++) {
			hash = text.charCodeAt(i) + ((hash << 5) - hash);
		}

		return colors[Math.abs(hash) % colors.length];
	}

	function handleError() {
		hasError = true;
	}
</script>

<div
	class={cn(
		'relative inline-flex items-center justify-center overflow-hidden rounded-full',
		config.size,
		className
	)}
	{...restProps}
>
	{#if src && !hasError}
		<LazyImage
			{src}
			{alt}
			{loading}
			width={config.pixels}
			height={config.pixels}
			class="h-full w-full object-cover"
			onError={handleError}
		/>
	{:else}
		<!-- Fallback with initials -->
		<div
			class={cn(
				'flex h-full w-full items-center justify-center bg-gradient-to-br font-semibold text-white',
				config.text,
				avatarColor
			)}
		>
			{initials}
		</div>
	{/if}
</div>
