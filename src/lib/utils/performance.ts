/**
 * Performance monitoring utilities for Core Web Vitals and custom metrics
 */

interface PerformanceMetric {
	name: string;
	value: number;
	rating: 'good' | 'needs-improvement' | 'poor';
	timestamp: number;
}

interface LayoutShiftEntry extends PerformanceEntry {
	value: number;
	hadRecentInput: boolean;
}

interface WebVitalsThresholds {
	good: number;
	needsImprovement: number;
}

// Core Web Vitals thresholds (in milliseconds)
const WEB_VITALS_THRESHOLDS: Record<string, WebVitalsThresholds> = {
	CLS: { good: 0.1, needsImprovement: 0.25 },
	FID: { good: 100, needsImprovement: 300 },
	LCP: { good: 2500, needsImprovement: 4000 },
	FCP: { good: 1800, needsImprovement: 3000 },
	TTFB: { good: 800, needsImprovement: 1800 },
	INP: { good: 200, needsImprovement: 500 }
};

/**
 * Get performance rating based on value and thresholds
 */
function getPerformanceRating(
	metricName: string,
	value: number
): 'good' | 'needs-improvement' | 'poor' {
	const thresholds = WEB_VITALS_THRESHOLDS[metricName];
	if (!thresholds) return 'good';

	if (value <= thresholds.good) return 'good';
	if (value <= thresholds.needsImprovement) return 'needs-improvement';
	return 'poor';
}

/**
 * Measure and report Core Web Vitals
 */
export class PerformanceMonitor {
	private metrics: PerformanceMetric[] = [];
	private observers: PerformanceObserver[] = [];

	constructor(private onMetric?: (metric: PerformanceMetric) => void) {
		this.initializeObservers();
	}

	private initializeObservers(): void {
		if (typeof window === 'undefined') return;

		// Largest Contentful Paint (LCP)
		this.observeMetric('largest-contentful-paint', (entries) => {
			const lastEntry = entries[entries.length - 1];
			this.reportMetric('LCP', lastEntry.startTime);
		});

		// First Input Delay (FID)
		this.observeMetric('first-input', (entries) => {
			const firstEntry = entries[0];
			this.reportMetric('FID', firstEntry.processingStart - firstEntry.startTime);
		});

		// Cumulative Layout Shift (CLS)
		this.observeMetric('layout-shift', (entries) => {
			let clsValue = 0;
			for (const entry of entries) {
				const layoutEntry = entry as LayoutShiftEntry;
				if (!layoutEntry.hadRecentInput) {
					clsValue += layoutEntry.value;
				}
			}
			this.reportMetric('CLS', clsValue);
		});

		// First Contentful Paint (FCP)
		this.observeMetric('paint', (entries) => {
			const fcpEntry = entries.find((entry) => entry.name === 'first-contentful-paint');
			if (fcpEntry) {
				this.reportMetric('FCP', fcpEntry.startTime);
			}
		});

		// Navigation timing for TTFB
		this.observeMetric('navigation', (entries) => {
			const navEntry = entries[0] as PerformanceNavigationTiming;
			this.reportMetric('TTFB', navEntry.responseStart - navEntry.requestStart);
		});
	}

	private observeMetric(type: string, callback: (entries: PerformanceEntry[]) => void): void {
		try {
			const observer = new PerformanceObserver((list) => {
				callback(list.getEntries());
			});
			observer.observe({ type, buffered: true });
			this.observers.push(observer);
		} catch (error) {
			console.warn(`Failed to observe ${type}:`, error);
		}
	}

	private reportMetric(name: string, value: number): void {
		const metric: PerformanceMetric = {
			name,
			value,
			rating: getPerformanceRating(name, value),
			timestamp: Date.now()
		};

		this.metrics.push(metric);
		this.onMetric?.(metric);

		// Log to console in development
		if (import.meta.env.DEV) {
			console.log(`${name}: ${value.toFixed(2)}ms (${metric.rating})`);
		}
	}

	/**
	 * Get all collected metrics
	 */
	getMetrics(): PerformanceMetric[] {
		return [...this.metrics];
	}

	/**
	 * Get metrics by name
	 */
	getMetric(name: string): PerformanceMetric | undefined {
		return this.metrics.find((metric) => metric.name === name);
	}

	/**
	 * Measure custom timing
	 */
	measureTiming(name: string, startTime: number): void {
		const duration = performance.now() - startTime;
		this.reportMetric(name, duration);
	}

	/**
	 * Start a custom measurement
	 */
	startMeasurement(name: string): () => void {
		const startTime = performance.now();
		return () => this.measureTiming(name, startTime);
	}

	/**
	 * Disconnect all observers
	 */
	disconnect(): void {
		this.observers.forEach((observer) => observer.disconnect());
		this.observers = [];
	}
}

/**
 * Simple performance mark utility
 */
export function mark(name: string): void {
	if (typeof performance !== 'undefined' && performance.mark) {
		performance.mark(name);
	}
}

/**
 * Measure time between two marks
 */
export function measure(name: string, startMark: string, endMark?: string): number | null {
	if (typeof performance === 'undefined' || !performance.measure) return null;

	try {
		if (endMark) {
			performance.measure(name, startMark, endMark);
		} else {
			performance.measure(name, startMark);
		}

		const entries = performance.getEntriesByName(name, 'measure');
		const lastEntry = entries[entries.length - 1];
		return lastEntry ? lastEntry.duration : null;
	} catch (error) {
		console.warn('Failed to measure performance:', error);
		return null;
	}
}

/**
 * Get navigation timing information
 */
export function getNavigationTiming(): Record<string, number> | null {
	if (typeof performance === 'undefined' || !performance.getEntriesByType) return null;

	const navEntries = performance.getEntriesByType('navigation') as PerformanceNavigationTiming[];
	if (navEntries.length === 0) return null;

	const nav = navEntries[0];
	return {
		dns: nav.domainLookupEnd - nav.domainLookupStart,
		tcp: nav.connectEnd - nav.connectStart,
		ttfb: nav.responseStart - nav.requestStart,
		download: nav.responseEnd - nav.responseStart,
		domParse: nav.domContentLoadedEventStart - nav.responseEnd,
		domReady: nav.domContentLoadedEventEnd - nav.domContentLoadedEventStart,
		load: nav.loadEventEnd - nav.loadEventStart,
		total: nav.loadEventEnd - nav.navigationStart
	};
}
