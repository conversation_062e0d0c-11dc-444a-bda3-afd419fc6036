/**
 * Image preloader utility for performance optimization
 */

interface PreloadOptions {
	priority?: 'high' | 'low';
	as?: 'image';
	crossorigin?: 'anonymous' | 'use-credentials';
}

/**
 * Preload an image by creating a link element with rel="preload"
 */
export function preloadImage(src: string, options: PreloadOptions = {}): void {
	if (typeof window === 'undefined') return;

	// Check if already preloaded
	const existing = document.querySelector(`link[href="${src}"]`);
	if (existing) return;

	const link = document.createElement('link');
	link.rel = 'preload';
	link.as = options.as || 'image';
	link.href = src;

	if (options.priority) {
		link.setAttribute('fetchpriority', options.priority);
	}

	if (options.crossorigin) {
		link.crossOrigin = options.crossorigin;
	}

	document.head.appendChild(link);
}

/**
 * Preload multiple images
 */
export function preloadImages(sources: string[], options: PreloadOptions = {}): void {
	sources.forEach((src) => preloadImage(src, options));
}

/**
 * Create an optimized image URL with format and size parameters
 * This is useful for services like Dicebear that support query parameters
 */
export function optimizeImageUrl(
	baseUrl: string,
	options: {
		format?: 'svg' | 'png' | 'jpg' | 'webp';
		size?: number;
		quality?: number;
	} = {}
): string {
	const url = new URL(baseUrl);

	if (options.format && options.format !== 'svg') {
		url.searchParams.set('format', options.format);
	}

	if (options.size) {
		url.searchParams.set('size', options.size.toString());
	}

	if (options.quality && options.format !== 'svg') {
		url.searchParams.set('quality', options.quality.toString());
	}

	return url.toString();
}

/**
 * Generate responsive image srcset for different screen densities
 */
export function generateSrcSet(baseUrl: string, sizes: number[] = [1, 2, 3]): string {
	return sizes
		.map((density) => {
			const optimizedUrl = optimizeImageUrl(baseUrl, {
				size: 48 * density, // Base size * density
				quality: 85
			});
			return `${optimizedUrl} ${density}x`;
		})
		.join(', ');
}

/**
 * Check if WebP is supported
 */
export function supportsWebP(): Promise<boolean> {
	return new Promise((resolve) => {
		const webP = new Image();
		webP.onload = webP.onerror = () => {
			resolve(webP.height === 2);
		};
		webP.src =
			'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
	});
}

/**
 * Get the best image format based on browser support
 */
export async function getBestImageFormat(): Promise<'webp' | 'png'> {
	const webpSupported = await supportsWebP();
	return webpSupported ? 'webp' : 'png';
}
